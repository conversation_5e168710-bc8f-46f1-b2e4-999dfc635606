---
import Ski<PERSON><PERSON>o<PERSON>ontentButton from "@/components/ui/SkipToContentButton.vue";
import MobileNavigation from "@/components/ui/MobileNavigation.vue";
import type { SiteInfo } from "@/data/portfolio";

interface Props {
  data: SiteInfo;
}

const { title, navigation, skipToContentText } = Astro.props.data;
---

<SkipToContentButton text={skipToContentText} targetId="main" client:load />

<header
  class="sticky top-0 z-40 border-b border-slate-200 bg-white/90 backdrop-blur supports-[backdrop-filter]:bg-white/70"
>
  <nav
    class="mx-auto flex max-w-6xl items-center justify-between px-4 py-3 sm:px-6"
    aria-label="Main navigation"
  >
    <!-- Responsive Brand Name -->
    <div class="font-bold tracking-tight text-sky-900">
      <!-- Full brand name on larger screens -->
      <span class="hidden sm:inline">{title}</span>
      <!-- Shortened brand name on mobile -->
      <span class="sm:hidden">Portfolio</span>
    </div>

    <!-- Desktop Navigation -->
    <ul class="hidden gap-6 text-sm lg:flex">
      {
        navigation.map((item) => (
          <li>
            <a
              href={item.href}
              class="rounded px-2 py-1 transition-colors duration-200 hover:text-sky-800 focus:ring-2 focus:ring-sky-600 focus:outline-none"
            >
              {item.label}
            </a>
          </li>
        ))
      }
    </ul>

    <!-- Mobile Navigation -->
    <MobileNavigation navigation={navigation} client:load />
  </nav>
</header>
