import { defineConfig } from "eslint/config";
import eslintPluginAstro from "eslint-plugin-astro";
import jsxA11y from "eslint-plugin-jsx-a11y";

export default defineConfig([
    // add more generic rule sets here, such as:
    // js.configs.recommended,
    jsxA11y.flatConfigs.recommended,
    ...eslintPluginAstro.configs.recommended,
    {
        rules: {
            // override/add rules settings here, such as:
            "prefer-const": "error",
            // "astro/no-set-html-directive": "error"
        },
    },
]);
