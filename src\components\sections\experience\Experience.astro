---
import CollapsibleList from "@/components/ui/CollapsibleList.vue";
import { Employment, Qualification, Certification } from ".";
import type { Sections } from "@/data/portfolio";

interface Props {
  data: Sections["experiences"];
}

const {
  title,
  employments: employmentData,
  qualifications: qualificationData,
  certifications: certificationData,
} = Astro.props.data;

const { title: employmentTitle, employments } = employmentData;
const { title: qualificationTitle, qualifications } = qualificationData;
const { title: certificationTitle, certifications } = certificationData;

const visibleCount = 2;

// Employment data
const visibleEmployments = employments.slice(0, visibleCount);
const hiddenEmployments = employments.slice(visibleCount);

// Qualifications data
const visibleQualifications = qualifications.slice(0, visibleCount);
const hiddenQualifications = qualifications.slice(visibleCount);

// Certifications data
const visibleCertifications = certifications.slice(0, visibleCount);
const hiddenCertifications = certifications.slice(visibleCount);
---

<section
  id="experience"
  aria-labelledby="exp-title"
  class="border-t border-slate-200 py-10"
>
  <h2 id="exp-title" class="text-2xl font-bold text-sky-900">{title}</h2>

  <div class="mt-6 space-y-8">
    {
      employments && employments.length > 0 && (
        <section aria-labelledby="employment-title">
          <h3 id="employment-title" class="font-bold text-sky-900">
            {employmentTitle}
          </h3>

          <div class="mt-3 hidden gap-4 md:grid md:grid-cols-2 md:gap-6">
            {employments.map((employment) => (
              <Employment employment={employment} />
            ))}
          </div>

          <CollapsibleList class="mt-3 md:hidden" client:visible>
            <div slot="visibleItems" class="grid gap-4">
              {visibleEmployments.map((employment) => (
                <Employment employment={employment} />
              ))}
            </div>

            <div slot="hiddenItems" class="mt-4 grid gap-4">
              {hiddenEmployments.map((employment) => (
                <Employment employment={employment} />
              ))}
            </div>
          </CollapsibleList>
        </section>
      )
    }

    {
      qualifications && qualifications.length > 0 && (
        <section
          aria-labelledby="degrees-title"
          class="border-t border-slate-100 pt-6"
        >
          <h3 id="degrees-title" class="font-bold text-sky-900">
            {qualificationTitle}
          </h3>

          <div class="mt-3 hidden gap-4 md:grid md:grid-cols-2 md:gap-6">
            {qualifications.map((qualification) => (
              <Qualification qualification={qualification} />
            ))}
          </div>

          <CollapsibleList class="mt-3 md:hidden" client:visible>
            <div slot="visibleItems" class="grid gap-4">
              {visibleQualifications.map((qualification) => (
                <Qualification qualification={qualification} />
              ))}
            </div>

            <div slot="hiddenItems" class="mt-4 grid gap-4">
              {hiddenQualifications.map((qualification) => (
                <Qualification qualification={qualification} />
              ))}
            </div>
          </CollapsibleList>
        </section>
      )
    }

    {
      certifications && certifications.length > 0 && (
        <section
          aria-labelledby="certs-title"
          class="border-t border-slate-100 pt-6"
        >
          <h3 id="certs-title" class="font-bold text-sky-900">
            {certificationTitle}
          </h3>

          <div class="mt-3 hidden gap-4 md:grid md:grid-cols-2 md:gap-6">
            {certifications.map((certification) => (
              <Certification certification={certification} />
            ))}
          </div>

          <CollapsibleList class="mt-3 md:hidden" client:visible>
            <div slot="visibleItems" class="grid gap-4">
              {visibleCertifications.map((certification) => (
                <Certification certification={certification} />
              ))}
            </div>

            <div slot="hiddenItems" class="mt-4 grid gap-4">
              {hiddenCertifications.map((certification) => (
                <Certification certification={certification} />
              ))}
            </div>
          </CollapsibleList>
        </section>
      )
    }
  </div>
</section>
