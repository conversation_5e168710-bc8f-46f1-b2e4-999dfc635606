# build output
dist/
# generated types
.astro/

# dependencies
node_modules/

# logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*


# environment variables
.env
.env.production

# macOS-specific files
.DS_Store

# jetbrains setting folder
.idea/

# vscode setting folder
.vscode/

# requirements folder
requirements/

# AI Files
TESTING_MIGRATION.md
TESTING_GUIDE.md
src/data/cv-ori.md
src/data/cv.md