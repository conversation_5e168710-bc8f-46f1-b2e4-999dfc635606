<script setup lang="ts">
import { ref, onMounted, onUnmounted } from "vue";
import { Menu, X } from "lucide-vue-next";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface NavigationItem {
  href: string;
  label: string;
}

interface Props {
  navigation: NavigationItem[];
}

const props = defineProps<Props>();
const isOpen = ref(false);
const mobileNavRef = ref<HTMLElement>();

const toggleMenu = () => {
  isOpen.value = !isOpen.value;
};

const closeMenu = () => {
  isOpen.value = false;
};

// Close menu when clicking outside
const handleClickOutside = (event: Event) => {
  if (!isOpen.value || !mobileNavRef.value) return;

  const target = event.target as Element;

  if (!mobileNavRef.value.contains(target)) {
    closeMenu();
  }
};

// Close menu on escape key
const handleEscapeKey = (event: KeyboardEvent) => {
  if (event.key === "Escape") {
    closeMenu();
  }
};

onMounted(() => {
  document.addEventListener("click", handleClickOutside);
  document.addEventListener("keydown", handleEscapeKey);
});

onUnmounted(() => {
  document.removeEventListener("click", handleClickOutside);
  document.removeEventListener("keydown", handleEscapeKey);
});
</script>

<template>
  <div class="lg:hidden" ref="mobileNavRef">
    <DropdownMenu v-model:open="isOpen">
      <DropdownMenuTrigger as-child>
        <Button
          variant="ghost"
          size="icon"
          class="h-9 w-9 text-sky-900 hover:bg-sky-50 focus:ring-2 focus:ring-sky-600 focus:ring-offset-2"
          :aria-label="
            isOpen ? 'Close navigation menu' : 'Open navigation menu'
          "
          :aria-expanded="isOpen"
          aria-controls="mobile-navigation-menu"
        >
          <Menu v-if="!isOpen" class="h-5 w-5" />
          <X v-else class="h-5 w-5" />
        </Button>
      </DropdownMenuTrigger>

      <DropdownMenuContent
        id="mobile-navigation-menu"
        align="end"
        class="animate-in slide-in-from-top-2 mt-2 w-64 duration-200 max-[400px]:w-[calc(100dvw-2rem)]"
        :side-offset="8"
      >
        <DropdownMenuItem
          v-for="item in navigation"
          :key="item.href"
          as-child
          class="mb-1 flex min-h-[44px] w-full cursor-pointer items-center px-4 py-3 text-lg text-slate-700 transition-colors duration-200 last:mb-0 hover:text-sky-800 focus:bg-sky-50 focus:text-sky-900 focus:outline-none"
        >
          <a :href="item.href" @click="closeMenu">
            {{ item.label }}
          </a>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  </div>
</template>

<style scoped>
/* Ensure smooth transitions */
.animate-in {
  animation-fill-mode: both;
}

@keyframes slide-in-from-top-2 {
  from {
    opacity: 0;
    transform: translateY(-8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in-from-top-2 {
  animation: slide-in-from-top-2 0.2s ease-out;
}
</style>
