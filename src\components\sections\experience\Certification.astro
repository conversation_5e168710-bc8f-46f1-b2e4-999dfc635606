---
import type { Sections } from "@/data/portfolio";

interface Props {
  certification: Sections["experiences"]["certifications"]["certifications"][0];
}

const { certification } = Astro.props;

const IconComponent = certification.icon;
---

<article
  class="flex h-full flex-col rounded border border-slate-200 bg-white p-4"
>
  <div class="flex items-start gap-4 max-[320px]:flex-col">
    <div
      class="mt-1 grid h-24 w-24 flex-shrink-0 place-items-center rounded-md bg-slate-100"
    >
      <IconComponent size={40} class="text-sky-600" />
    </div>

    <div class="min-w-0 flex-1">
      <h4 class="mb-1 text-lg font-bold text-sky-900">{certification.title}</h4>
      <p class="mb-2 text-base font-bold text-sky-600 italic">
        {certification.issued}
      </p>

      <div class="space-y-1 text-sm text-slate-600">
        <p>Issuer: {certification.issuer}</p>
        <p>Location: {certification.location}</p>
      </div>
    </div>
  </div>
</article>
