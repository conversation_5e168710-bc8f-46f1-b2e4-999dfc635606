---
import { Picture } from "astro:assets";
import { AspectRatio } from "@/components/ui/aspect-ratio";
import type { Sections } from "@/data/portfolio";

interface Props {
  proficiency: Sections["proficiency"]["proficiency"][0];
}

const { proficiency } = Astro.props;
---

<article class="rounded border border-slate-200 bg-white p-4">
  <AspectRatio ratio={3 / 2}>
    <Picture
      src={proficiency.image}
      alt={proficiency.alt}
      formats={["avif", "webp"]}
      quality="high"
      layout="full-width"
      class="h-full w-full"
    />
  </AspectRatio>
  <h3 class="mt-4 text-lg font-bold text-sky-900">{proficiency.name}</h3>
  <p class="mt-2 text-xs text-slate-600">{proficiency.description}</p>
</article>
