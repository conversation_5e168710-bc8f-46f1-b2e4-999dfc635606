---
import CollapsibleList from "@/components/ui/CollapsibleList.vue";
import { ProficiencyCard } from ".";
import type { Sections } from "@/data/portfolio";

interface Props {
  data: Sections["proficiency"];
}

const { title, proficiency } = Astro.props.data;

// Prepare data for collapsible section (show first 2 items on mobile)
const visibleCount = 2;
const visibleProficiency = proficiency.slice(0, visibleCount);
const hiddenProficiency = proficiency.slice(visibleCount);
---

<section id="proficiency" class="border-t border-slate-200 py-10">
  <h2 class="text-2xl font-bold text-sky-900">{title}</h2>

  <div class="mt-4 hidden gap-4 sm:grid-cols-2 md:grid">
    {
      proficiency.map((proficiency) => (
        <ProficiencyCard proficiency={proficiency} />
      ))
    }
  </div>

  <CollapsibleList class="mt-4 md:hidden" client:visible>
    <div slot="visibleItems" class="grid gap-4">
      {
        visibleProficiency.map((proficiency) => (
          <ProficiencyCard proficiency={proficiency} />
        ))
      }
    </div>

    <div slot="hiddenItems" class="mt-4 grid gap-4">
      {
        hiddenProficiency.map((proficiency) => (
          <ProficiencyCard proficiency={proficiency} />
        ))
      }
    </div>
  </CollapsibleList>
</section>
