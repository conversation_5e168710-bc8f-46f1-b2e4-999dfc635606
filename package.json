{"name": "portfolio-mr-<PERSON>h", "type": "module", "version": "0.0.1", "scripts": {"dev": "astro dev", "build": "astro build", "preview": "astro preview", "astro": "astro", "lint": "eslint \"src/**/*.{js,astro}\"", "lint:fix": "eslint \"src/**/*.{js,astro}\" --fix", "format": "pnpm exec prettier ./src --write"}, "dependencies": {"@astrojs/check": "^0.9.4", "@astrojs/vue": "^5.1.0", "@tailwindcss/vite": "^4.1.3", "@vueuse/core": "^13.7.0", "astro": "^5.12.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-vue-next": "^0.539.0", "reka-ui": "^2.4.1", "sharp": "^0.34.3", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.3", "tw-animate-css": "^1.3.6", "typescript": "^5.9.2", "vue": "^3.5.18"}, "packageManager": "pnpm@10.15.0+sha512.486ebc259d3e999a4e8691ce03b5cac4a71cbeca39372a9b762cb500cfdf0873e2cb16abe3d951b1ee2cf012503f027b98b6584e4df22524e0c7450d9ec7aa7b", "devDependencies": {"@typescript-eslint/parser": "^8.41.0", "eslint": "^9.34.0", "eslint-plugin-astro": "^1.3.1", "eslint-plugin-jsx-a11y": "^6.10.2", "prettier": "3.6.2", "prettier-plugin-astro": "0.14.1", "prettier-plugin-tailwindcss": "^0.6.14"}}