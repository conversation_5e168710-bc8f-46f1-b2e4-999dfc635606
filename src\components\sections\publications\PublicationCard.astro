---
import { Picture } from "astro:assets";
import { AspectRatio } from "@/components/ui/aspect-ratio";
import { cn } from "@/lib/utils";
import { typeStyles, type Publication } from ".";

interface Props {
  publication: Publication;
  index: number;
  type: "published" | "unpublished";
}

const { publication, index, type } = Astro.props;

const currentStyles = typeStyles[type];
const formattedIndex = `[${index + 1}]`;

let publicationTitle: string | undefined;
const notes = JSON.parse(JSON.stringify(publication.notes));

if (type === "unpublished" && Array.isArray(notes) && notes.length > 1) {
  publicationTitle =
    publication.title +
    notes.reduce((prev, curr, i, arr) => {
      if (i === arr.length - 1) return prev;
      return prev + curr;
    }, "");
  notes.splice(0, notes.length - 1);
}
---

<article class="flex flex-row gap-4 max-[340px]:flex-col">
  <!-- Left Side: Index Number and Thumbnail (outside card) -->
  <div class="relative w-24 flex-shrink-0">
    <AspectRatio ratio={210 / 297}>
      <Picture
        src={publication.thumbnail}
        alt="Publication thumbnail"
        width={96}
        widths={[96, 192, publication.thumbnail.width]}
        sizes="96px"
        formats={["avif", "webp"]}
        priority={index === 0}
      />
    </AspectRatio>
    <!-- Index number overlay on thumbnail -->
    <div
      class={cn(
        "absolute -top-2 -left-2 p-1 rounded-sm text-white grid place-items-center text-sm font-bold shadow-sm",
        currentStyles.bgSecondary,
        currentStyles.textPrimary,
      )}
    >
      {formattedIndex}
    </div>
  </div>

  <!-- Right Side: Content Card -->
  <div
    class="flex w-full flex-col rounded-lg border border-slate-200 bg-white p-3 shadow-sm sm:p-4"
  >
    <span
      class="mb-1 text-sm break-words text-slate-500"
      set:html={publication.authors}
    />
    <h4
      class="mb-1 leading-tight font-bold break-words text-slate-800"
      set:html={publicationTitle ?? publication.title}
    />
    <span
      class="mb-2 text-sm break-words text-slate-600"
      set:html={publication.journal}
    />

    <div class="flex flex-wrap items-center gap-2 text-sm sm:gap-4">
      {
        notes &&
          Array.isArray(notes) &&
          notes.length > 0 &&
          notes[0] !== "" && (
            <span class="rounded-sm bg-slate-100 px-2 py-1 font-bold">
              {notes.map((note, index) => (
                <span
                  class={cn(
                    "break-words",
                    index === 0 ? "text-red-600" : "text-green-600",
                  )}
                  set:html={note}
                />
              ))}
            </span>
          )
      }
      {
        publication.link && (
          <a
            href={publication.link}
            class={cn(
              "break-words underline",
              currentStyles.textPrimary,
              currentStyles.textHover,
            )}
          >
            View publication
          </a>
        )
      }
    </div>
  </div>
</article>
