---
import { Image } from "astro:assets";
import type { Sections } from "@/data/portfolio";

interface Props {
  employment: Sections["experiences"]["employments"]["employments"][0];
}

const { employment } = Astro.props;
---

<article
  class="flex h-full flex-col rounded border border-slate-200 bg-white p-4"
>
  <div class="flex items-start gap-4 max-[320px]:flex-col">
    <div class="overflow-hidden rounded-md bg-slate-100">
      <Image
        src={employment.logo}
        alt=""
        width={96}
        height={96}
        class="h-24 w-24 flex-shrink-0"
      />
    </div>

    <div class="min-w-0 flex-1">
      <h4 class="mb-1 text-lg font-bold text-sky-900">
        {employment.organization}
      </h4>
      <p class="mb-2 font-bold text-sky-600 italic">{employment.position}</p>

      <div class="mb-2 space-y-1 text-sm text-slate-600">
        <p>Period: {employment.period}</p>
        <p>Location: {employment.location}</p>
        <p>Employment Type: {employment.employmentType}</p>
      </div>
    </div>
  </div>
</article>
